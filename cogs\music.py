"""
Modern Music module for Discord bot using Wavelink.

This module provides a complete music bot implementation using Wavelink
for reliable audio streaming without requiring local FFmpeg installation.
Features include YouTube, Spotify, and SoundCloud support with queue management,
volume control, and various playback controls.
"""

import asyncio
import discord
import wavelink
from discord.ext import commands
import random
import re

try:
    from utilities import checks, decorators, converters
    from utilities import spotify
except ImportError:
    checks = decorators = converters = None
    spotify = None

try:
    from settings import constants
except ImportError:
    constants = None

# Spotify URL patterns
SPOTIFY_TRACK_REGEX = r"https://open\.spotify\.com/track/([a-zA-Z0-9]+)"
SPOTIFY_PLAYLIST_REGEX = r"https://open\.spotify\.com/playlist/([a-zA-Z0-9]+)"
SPOTIFY_ALBUM_REGEX = r"https://open\.spotify\.com/album/([a-zA-Z0-9]+)"


class MusicControlView(discord.ui.View):
    def __init__(self, player, ctx):
        super().__init__(timeout=None)
        self.player = player
        self.ctx = ctx

    async def interaction_check(self, interaction: discord.Interaction) -> bool:
        if not self.ctx.voice_client or not self.player.playing:
            await interaction.response.send_message("I'm not currently playing music.", ephemeral=True)
            return False
        if interaction.user in self.ctx.voice_client.channel.members:
            return True
        await interaction.response.send_message(
            "Only members in the same voice channel can control the player.", ephemeral=True
        )
        return False

    @discord.ui.button(emoji="<:o_pause:1302172935951351821>", style=discord.ButtonStyle.success)
    async def pause_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.player.paused:
            await self.player.pause(False)
            # Update voice channel status to show playing
            await self.player.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{self.player.current.title}** - {self.player.current.author}")
            button.emoji = "<:o_pause:1302172935951351821>"
            await interaction.response.edit_message(view=self)
        elif self.player.playing:
            await self.player.pause(True)
            # Update voice channel status to show paused
            await self.player.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{self.player.current.title}** - {self.player.current.author}")
            button.emoji = "<:o_resume:1302173145980997712>"
            await interaction.response.edit_message(view=self)

    @discord.ui.button(emoji="<:forward2:1393199679012868136>", style=discord.ButtonStyle.secondary)
    async def skip_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.player and self.player.playing:
            await self.player.stop()
            await interaction.response.send_message(f"Skipped by **{interaction.user.display_name}**.")
        else:
            await interaction.response.send_message("No song to skip.", ephemeral=True)

    @discord.ui.button(emoji="<:loop:1396520104279281724>", style=discord.ButtonStyle.secondary)
    async def loop_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        self.player.queue.mode = wavelink.QueueMode.loop if self.player.queue.mode != wavelink.QueueMode.loop else wavelink.QueueMode.normal
        await interaction.response.send_message(f"Loop {'enabled' if self.player.queue.mode == wavelink.QueueMode.loop else 'disabled'}.")

    @discord.ui.button(emoji="<:shuffle:1396520272642838579>", style=discord.ButtonStyle.secondary)
    async def shuffle_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.player.queue:
            random.shuffle(self.player.queue)
            await interaction.response.send_message(f"Queue shuffled by **{interaction.user.display_name}**.")
        else:
            await interaction.response.send_message("Queue is empty.", ephemeral=True)

    @discord.ui.button(emoji="<:stop:1395848153265213581>", style=discord.ButtonStyle.danger)
    async def stop_button(self, interaction: discord.Interaction, button: discord.ui.Button):
        if self.player:
            # Clear voice channel status before disconnecting
            try:
                await self.player.channel.edit(status=None)
            except:
                pass  # Ignore errors if channel doesn't exist or no permissions
            await self.player.disconnect()
            await interaction.response.send_message(f"Stopped and disconnected by **{interaction.user.display_name}**.")
        else:
            await interaction.response.send_message("Not connected.", ephemeral=True)


class Music(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.bot.loop.create_task(self.connect_nodes())
        # Dictionary to store track requesters {guild_id: {track_id: user}}
        self.track_requesters = {}

    async def connect_nodes(self) -> None:
        """Connect to Lavalink nodes using Wavelink v3+"""
        await wavelink.Pool.connect(
            client=self.bot,
            nodes=[
                wavelink.Node(
                    uri="http://*************:9376",  # Replace if not HTTPS
                    password="CodeX"           # Replace with actual password
                )
            ]
        )

    async def display_player_embed(self, player, track, ctx):
        duration = f"{track.length // 60000}:{(track.length // 1000) % 60:02d}"

        embed = discord.Embed(
            title=f"<a:MusicNotes:1393302817053081690> Now Playing",
            description=f"> **[{track.title}]({track.uri})**",
            color=0x333239
        )
        embed.add_field(name="Artist", value=track.author, inline=True)
        embed.add_field(name="Duration", value=duration, inline=True)
        embed.add_field(name="Source", value="YouTube", inline=True)

        if track.artwork:
            embed.set_thumbnail(url=track.artwork)

        embed.set_footer(
            text=f"Requested by {ctx.author.display_name}",
            icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url
        )

        await ctx.send(embed=embed, view=MusicControlView(player, ctx))

    def store_track_requester(self, guild_id, track, user):
        """Store who requested a track"""
        if guild_id not in self.track_requesters:
            self.track_requesters[guild_id] = {}

        # Use track URI as unique identifier
        track_id = track.uri if hasattr(track, 'uri') else str(track)
        self.track_requesters[guild_id][track_id] = user

    def get_track_requester(self, guild_id, track):
        """Get who requested a track"""
        if guild_id not in self.track_requesters:
            return None

        track_id = track.uri if hasattr(track, 'uri') else str(track)
        return self.track_requesters[guild_id].get(track_id)

    @commands.Cog.listener()
    async def on_wavelink_track_start(self, payload: wavelink.TrackStartEventPayload):
        player = payload.player
        track = player.current
        print(f"[MUSIC] Started playing: {track.title}")

        # Update voice channel status when track starts
        try:
            await player.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{track.title}** - {track.author}")
        except:
            pass  # Ignore errors if channel doesn't exist or no permissions

    @commands.Cog.listener()
    async def on_wavelink_track_end(self, payload: wavelink.TrackEndEventPayload):
        player = payload.player

        if not player.queue.is_empty:
            next_track = await player.queue.get_wait()
            await player.play(next_track)
            if hasattr(player, 'ctx'):
                await self.display_player_embed(player, next_track, player.ctx)
        else:
            await asyncio.sleep(30)
            if player.queue.is_empty and not player.playing:
                # Clear voice channel status before disconnecting
                try:
                    await player.channel.edit(status=None)
                except:
                    pass  # Ignore errors if channel doesn't exist or no permissions
                await player.disconnect()
                if hasattr(player, 'ctx'):
                    embed = discord.Embed(
                        description="Queue ended. Disconnected from voice channel.",
                        color=0x333239
                    )
                    await player.ctx.send(embed=embed)

    @commands.command(name="play", aliases=["p"], brief="Play a song from YouTube or Spotify.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def play(self, ctx, *, query: str):
        if not ctx.author.voice:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  You need to be in a voice channel.", color=0x333239))
            return

        voice_channel = ctx.author.voice.channel
        perms = voice_channel.permissions_for(ctx.guild.me)

        if not perms.connect or not perms.speak:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  I need connect & speak permissions.", color=0x333239))
            return

        vc = ctx.voice_client or await voice_channel.connect(cls=wavelink.Player)
        vc.ctx = ctx

        # Check if it's a Spotify URL
        if spotify and ("open.spotify.com" in query or query.startswith("spotify:")):
            await self.handle_spotify_link(ctx, vc, query)
            return

        tracks = await wavelink.Playable.search(query)
        if not tracks:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  No results found.", color=0x333239))
            return

        if isinstance(tracks, wavelink.Playlist):
            # Store requester for all tracks in playlist
            for track in tracks.tracks:
                self.store_track_requester(ctx.guild.id, track, ctx.author)

            await vc.queue.put_wait(tracks.tracks)
            await ctx.send(embed=discord.Embed(description=f"<:pass:1395855305270755399> Added playlist **{tracks.name}**.", color=0x333239))

            if not vc.playing:
                track = await vc.queue.get_wait()
                await vc.play(track)
                await self.display_player_embed(vc, track, ctx)
        else:
            track = tracks[0]
            # Store requester for single track
            self.store_track_requester(ctx.guild.id, track, ctx.author)

            if not vc.playing:
                await vc.play(track)
                await ctx.send(embed=discord.Embed(description=f"<a:MusicNotes:1393302817053081690> Now playing **{track.title}**", color=0x333239))
                await self.display_player_embed(vc, track, ctx)
            else:
                await vc.queue.put_wait(track)
                await ctx.send(embed=discord.Embed(description=f"<:pass:1395855305270755399> Added **{track.title}** to the queue.", color=0x333239))

    @commands.command(name="skip", brief="Skip the current song.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def skip(self, ctx):
        vc = ctx.voice_client
        if not vc or not vc.playing:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  No song playing.", color=0x333239))
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  You must be in the same voice channel.", color=0x333239))
            return

        await vc.stop()
        await ctx.send(embed=discord.Embed(description="⏭️ Skipped the current song.", color=0x333239))

    @commands.command(name="stop", brief="Stop music and disconnect.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def stop(self, ctx):
        vc = ctx.voice_client
        if not vc:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Not connected to a voice channel.", color=0x333239))
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  You must be in the same voice channel.", color=0x333239))
            return

        vc.queue.clear()
        # Clear voice channel status before disconnecting
        try:
            await vc.channel.edit(status=None)
        except:
            pass  # Ignore errors if channel doesn't exist or no permissions
        await vc.disconnect()
        await ctx.send(embed=discord.Embed(description="⏹️ Stopped and disconnected.", color=0x333239))

    @commands.command(name="pause", brief="Pause the current song.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def pause(self, ctx):
        vc = ctx.voice_client
        if not vc or not vc.playing:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  No song playing.", color=0x333239))
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  You must be in the same voice channel.", color=0x333239))
            return

        if not vc.paused:
            await vc.pause(True)
            # Update voice channel status to show paused
            try:
                await vc.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{vc.current.title}** - {vc.current.author}")
            except:
                pass  # Ignore errors if channel doesn't exist or no permissions
            await ctx.send(embed=discord.Embed(description="⏸️ Paused the current song.", color=0x333239))
        else:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Already paused.", color=0x333239))

    @commands.command(name="resume", brief="Resume the paused song.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def resume(self, ctx):
        vc = ctx.voice_client
        if not vc or not vc.playing:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  No song playing.", color=0x333239))
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  You must be in the same voice channel.", color=0x333239))
            return

        if vc.paused:
            await vc.pause(False)
            # Update voice channel status to show playing
            try:
                await vc.channel.edit(status=f"<a:MusicNotes:1393302817053081690> **{vc.current.title}** - {vc.current.author}")
            except:
                pass  # Ignore errors if channel doesn't exist or no permissions
            await ctx.send(embed=discord.Embed(description="▶️ Resumed the current song.", color=0x333239))
        else:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Song is not paused.", color=0x333239))

    @commands.command(name="queue", aliases=["q"], brief="Show the current queue.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def queue(self, ctx):
        """
        Usage: {0}queue
        Alias: {0}q
        Output: Shows the current music queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  The queue is empty.", color=0x333239)
            await ctx.send(embed=embed)
            return

        embed = discord.Embed(title="<a:MusicNotes:1393302817053081690> Music Queue", color=0x333239)

        if vc.current:
            embed.add_field(
                name="<a:MusicNotes:1393302817053081690> Now Playing",
                value=f"**[{vc.current.title}]({vc.current.uri})**\nBy: {vc.current.author}",
                inline=False
            )

        if not vc.queue.is_empty:
            queue_list = []
            for i, track in enumerate(vc.queue[:10], 1):  # Show first 10 tracks
                queue_list.append(f"`{i}.` **[{track.title}]({track.uri})**")

            embed.add_field(
                name=f"📋 Up Next ({len(vc.queue)} tracks)",
                value="\n".join(queue_list),
                inline=False
            )

            if len(vc.queue) > 10:
                embed.add_field(
                    name="➕ More",
                    value=f"And {len(vc.queue) - 10} more tracks...",
                    inline=False
                )

        await ctx.send(embed=embed)

    @commands.command(name="nowplaying", aliases=["np"], brief="Show current song info.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def nowplaying(self, ctx):
        """
        Usage: {0}nowplaying
        Alias: {0}np
        Output: Shows information about the currently playing song.
        """
        vc = ctx.voice_client
        if not vc or not vc.current:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  No song is currently playing.", color=0x333239)
            await ctx.send(embed=embed)
            return

        track = vc.current
        duration = f"{track.length // 60000}:{(track.length // 1000) % 60:02d}"
        position = f"{vc.position // 60000}:{(vc.position // 1000) % 60:02d}"

        embed = discord.Embed(
            title="<a:MusicNotes:1393302817053081690> Now Playing",
            description=f"> **[{track.title}]({track.uri})**",
            color=0x333239
        )
        embed.add_field(name="Artist", value=track.author, inline=True)
        embed.add_field(name="Duration", value=f"{position} / {duration}", inline=True)
        embed.add_field(name="Source", value="YouTube", inline=True)

        # Get the requester of the current track
        requester = self.get_track_requester(ctx.guild.id, track)

        if track.artwork:
            embed.set_thumbnail(url=track.artwork)

        # Set footer with requester information
        if requester:
            embed.set_footer(
                text=f"Requested by {requester.display_name}",
                icon_url=requester.avatar.url if requester.avatar else requester.default_avatar.url
            )
        else:
            embed.set_footer(
                text=f"Requested by {ctx.author.display_name}",
                icon_url=ctx.author.avatar.url if ctx.author.avatar else ctx.author.default_avatar.url
            )

        await ctx.send(embed=embed)

    @commands.command(name="volume", aliases=["vol"], brief="Set or show volume.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def volume(self, ctx, volume: int = None):
        """
        Usage: {0}volume [1-100]
        Alias: {0}vol
        Output: Sets the volume or shows current volume.
        """
        vc = ctx.voice_client
        if not vc:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  I'm not connected to a voice channel.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  You need to be in the same voice channel as me.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if volume is None:
            embed = discord.Embed(description=f"🔊 Current volume: **{vc.volume}%**", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not 1 <= volume <= 100:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  Volume must be between 1 and 100.", color=0x333239)
            await ctx.send(embed=embed)
            return

        await vc.set_volume(volume)
        embed = discord.Embed(description=f"🔊 Volume set to **{volume}%**", color=0x333239)
        await ctx.send(embed=embed)

    @commands.command(name="shuffle", brief="Shuffle the queue.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def shuffle(self, ctx):
        """
        Usage: {0}shuffle
        Output: Shuffles the current queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  The queue is empty.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  You need to be in the same voice channel as me.", color=0x333239)
            await ctx.send(embed=embed)
            return

        random.shuffle(vc.queue)
        embed = discord.Embed(description=f"🔀 Shuffled **{len(vc.queue)}** tracks in the queue.", color=0x333239)
        await ctx.send(embed=embed)

    @commands.command(name="clear", brief="Clear the queue.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def clear(self, ctx):
        """
        Usage: {0}clear
        Output: Clears all tracks from the queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  The queue is already empty.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  You need to be in the same voice channel as me.", color=0x333239)
            await ctx.send(embed=embed)
            return

        queue_size = len(vc.queue)
        vc.queue.clear()
        embed = discord.Embed(description=f"🗑️ Cleared **{queue_size}** tracks from the queue.", color=0x333239)
        await ctx.send(embed=embed)

    @commands.command(name="remove", brief="Remove a track from queue.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def remove(self, ctx, index: int):
        """
        Usage: {0}remove <position>
        Output: Removes a track at the specified position from the queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  The queue is empty.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  You need to be in the same voice channel as me.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not 1 <= index <= len(vc.queue):
            embed = discord.Embed(description=f"<:lcross:1393191711848529932>  Invalid position. Queue has **{len(vc.queue)}** tracks.", color=0x333239)
            await ctx.send(embed=embed)
            return

        removed_track = vc.queue[index - 1]
        del vc.queue[index - 1]
        embed = discord.Embed(description=f"🗑️ Removed **{removed_track.title}** from position **{index}**.", color=0x333239)
        await ctx.send(embed=embed)

    @commands.command(name="move", brief="Move a track in the queue.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def move(self, ctx, from_pos: int, to_pos: int):
        """
        Usage: {0}move <from> <to>
        Output: Moves a track from one position to another in the queue.
        """
        vc = ctx.voice_client
        if not vc or vc.queue.is_empty:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  The queue is empty.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  You need to be in the same voice channel as me.", color=0x333239)
            await ctx.send(embed=embed)
            return

        queue_len = len(vc.queue)
        if not (1 <= from_pos <= queue_len) or not (1 <= to_pos <= queue_len):
            embed = discord.Embed(description=f"<:lcross:1393191711848529932>  Invalid positions. Queue has **{queue_len}** tracks.", color=0x333239)
            await ctx.send(embed=embed)
            return

        track = vc.queue[from_pos - 1]
        del vc.queue[from_pos - 1]
        vc.queue.put_at_index(to_pos - 1, track)

        embed = discord.Embed(description=f"📋 Moved **{track.title}** from position **{from_pos}** to **{to_pos}**.", color=0x333239)
        await ctx.send(embed=embed)

    @commands.command(name="seek", brief="Seek to a position in the current track.")
    @commands.cooldown(1, 5, commands.BucketType.user)
    async def seek(self, ctx, *, time_str: str):
        """
        Usage: {0}seek <time>
        Examples: {0}seek 1:30, {0}seek 90
        Output: Seeks to the specified time in the current track.
        """
        vc = ctx.voice_client
        if not vc or not vc.current:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  No song is currently playing.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  You need to be in the same voice channel as me.", color=0x333239)
            await ctx.send(embed=embed)
            return

        # Parse time string (supports formats like "1:30" or "90")
        try:
            if ":" in time_str:
                parts = time_str.split(":")
                if len(parts) == 2:
                    minutes, seconds = int(parts[0]), int(parts[1])
                    seek_time = (minutes * 60 + seconds) * 1000
                else:
                    raise ValueError("Invalid time format")
            else:
                seek_time = int(time_str) * 1000
        except ValueError:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  Invalid time format. Use `MM:SS` or seconds.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if seek_time < 0 or seek_time > vc.current.length:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  Seek time is out of range.", color=0x333239)
            await ctx.send(embed=embed)
            return

        await vc.seek(seek_time)
        seek_display = f"{seek_time // 60000}:{(seek_time // 1000) % 60:02d}"
        embed = discord.Embed(description=f"⏩ Seeked to **{seek_display}**", color=0x333239)
        await ctx.send(embed=embed)

    @commands.command(name="loop", brief="Toggle loop mode.")
    @commands.cooldown(1, 3, commands.BucketType.user)
    async def loop(self, ctx, mode: str = None):
        """
        Usage: {0}loop [off/track/queue]
        Output: Toggles loop mode or sets specific loop mode.
        """
        vc = ctx.voice_client
        if not vc:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  I'm not connected to a voice channel.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if not ctx.author.voice or ctx.author.voice.channel.id != vc.channel.id:
            embed = discord.Embed(description="<:lcross:1393191711848529932>  You need to be in the same voice channel as me.", color=0x333239)
            await ctx.send(embed=embed)
            return

        if mode is None:
            # Toggle between normal and loop
            if vc.queue.mode == wavelink.QueueMode.normal:
                vc.queue.mode = wavelink.QueueMode.loop
                embed = discord.Embed(description="🔁 Loop mode **enabled** (current track).", color=0x333239)
            else:
                vc.queue.mode = wavelink.QueueMode.normal
                embed = discord.Embed(description="🔁 Loop mode **disabled**.", color=0x333239)
        else:
            mode = mode.lower()
            if mode in ["off", "disable", "normal"]:
                vc.queue.mode = wavelink.QueueMode.normal
                embed = discord.Embed(description="🔁 Loop mode **disabled**.", color=0x333239)
            elif mode in ["track", "song", "current"]:
                vc.queue.mode = wavelink.QueueMode.loop
                embed = discord.Embed(description="🔁 Loop mode **enabled** (current track).", color=0x333239)
            elif mode in ["queue", "all"]:
                vc.queue.mode = wavelink.QueueMode.loop_all
                embed = discord.Embed(description="🔁 Loop mode **enabled** (entire queue).", color=0x333239)
            else:
                embed = discord.Embed(description="<:lcross:1393191711848529932>  Invalid loop mode. Use `off`, `track`, or `queue`.", color=0x333239)

        await ctx.send(embed=embed)

    async def handle_spotify_link(self, ctx, vc, link):
        """Handle Spotify URLs and convert them to playable tracks"""
        try:
            # Convert URL to URI if needed
            if "open.spotify.com" in link:
                uri = spotify.url_to_uri(link)
            else:
                uri = link

            if not uri or not uri.startswith("spotify:"):
                await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Invalid Spotify URL.", color=0x333239))
                return

            # Determine type
            if re.match(SPOTIFY_TRACK_REGEX, link):
                await self.handle_spotify_track(ctx, vc, uri)
            elif re.match(SPOTIFY_PLAYLIST_REGEX, link):
                await self.handle_spotify_playlist(ctx, vc, uri)
            elif re.match(SPOTIFY_ALBUM_REGEX, link):
                await self.handle_spotify_album(ctx, vc, uri)
            else:
                # Try to determine from URI
                uri_parts = uri.split(":")
                if len(uri_parts) >= 3:
                    spotify_type = uri_parts[1]
                    if spotify_type == "track":
                        await self.handle_spotify_track(ctx, vc, uri)
                    elif spotify_type == "playlist":
                        await self.handle_spotify_playlist(ctx, vc, uri)
                    elif spotify_type == "album":
                        await self.handle_spotify_album(ctx, vc, uri)
                    else:
                        await ctx.send(embed=discord.Embed(description=f"<:lcross:1393191711848529932>  Unsupported Spotify type: {spotify_type}", color=0x333239))
                else:
                    await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Invalid Spotify URI format.", color=0x333239))

        except Exception as e:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Error processing Spotify link.", color=0x333239))

    async def handle_spotify_track(self, ctx, vc, uri):
        """Handle a single Spotify track"""
        try:
            track_data = await spotify.get_track(uri)

            # Create search query
            artists = ", ".join([artist["name"] for artist in track_data["artists"]])
            search_query = f"{track_data['name']} {artists}"

            # Search for track
            search_results = await wavelink.Playable.search(search_query)

            if not search_results:
                await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Could not find this track on YouTube.", color=0x333239))
                return

            track = search_results[0]
            # Store requester for Spotify track
            self.store_track_requester(ctx.guild.id, track, ctx.author)

            if not vc.playing:
                await vc.play(track)
                await ctx.send(embed=discord.Embed(description=f"<a:MusicNotes:1393302817053081690> Now playing **{track.title}** (from Spotify)", color=0x333239))
                await self.display_player_embed(vc, track, ctx)
            else:
                await vc.queue.put_wait(track)
                await ctx.send(embed=discord.Embed(description=f"<:pass:1395855305270755399> Added **{track.title}** to the queue (from Spotify)", color=0x333239))

        except Exception as e:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Could not play this Spotify track.", color=0x333239))

    async def handle_spotify_playlist(self, ctx, vc, uri):
        """Handle a Spotify playlist"""
        try:
            # Get playlist info
            playlist_data = await spotify.get_playlist(uri)
            playlist_name = playlist_data.get("name", "Unknown Playlist")

            # Get all tracks from playlist
            all_tracks = []
            offset = 0
            limit = 50

            await ctx.send(embed=discord.Embed(description=f"<a:MusicNotes:1393302817053081690> Loading tracks from **{playlist_name}**...", color=0x333239))

            while True:
                tracks_data = await spotify.get_playlist_tracks(uri, limit=limit, offset=offset)
                tracks = tracks_data.get("items", [])

                if not tracks:
                    break

                all_tracks.extend(tracks)

                if len(tracks) < limit:
                    break

                offset += limit

            if not all_tracks:
                await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  This playlist appears to be empty.", color=0x333239))
                return

            # Add tracks to queue
            added_count = 0
            failed_count = 0

            for item in all_tracks[:50]:  # Limit to first 50 tracks to avoid timeout
                try:
                    track = item.get("track")
                    if not track or track.get("is_local"):
                        continue

                    # Create search query
                    artists = ", ".join([artist["name"] for artist in track["artists"]])
                    search_query = f"{track['name']} {artists}"

                    # Search for track
                    search_results = await wavelink.Playable.search(search_query)

                    if search_results:
                        found_track = search_results[0]
                        # Store requester for playlist track
                        self.store_track_requester(ctx.guild.id, found_track, ctx.author)
                        await vc.queue.put_wait(found_track)
                        added_count += 1
                    else:
                        failed_count += 1

                except Exception:
                    failed_count += 1

            # Start playing if not already playing
            if not vc.playing and not vc.queue.is_empty:
                track = await vc.queue.get_wait()
                await vc.play(track)
                await self.display_player_embed(vc, track, ctx)

            # Send summary
            summary = f"<:pass:1395855305270755399> Added **{added_count}** tracks from **{playlist_name}** to the queue."
            if failed_count > 0:
                summary += f"\n⚠️ Could not find **{failed_count}** tracks."
            if len(all_tracks) > 50:
                summary += f"\n📝 Limited to first 50 tracks ({len(all_tracks)} total)."

            await ctx.send(embed=discord.Embed(description=summary, color=0x333239))

        except Exception as e:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Could not load this Spotify playlist.", color=0x333239))

    async def handle_spotify_album(self, ctx, vc, uri):
        """Handle a Spotify album"""
        try:
            # Get album info (using get_playlist since it works for albums too)
            album_data = await spotify.get_playlist(uri)
            album_name = album_data.get("name", "Unknown Album")

            # Get all tracks from album
            all_tracks = []
            offset = 0
            limit = 50

            await ctx.send(embed=discord.Embed(description=f"<a:MusicNotes:1393302817053081690> Loading tracks from **{album_name}**...", color=0x333239))

            while True:
                tracks_data = await spotify.get_album_tracks(uri, limit=limit, offset=offset)
                tracks = tracks_data.get("items", [])

                if not tracks:
                    break

                all_tracks.extend(tracks)

                if len(tracks) < limit:
                    break

                offset += limit

            if not all_tracks:
                await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  This album appears to be empty.", color=0x333239))
                return

            # Add tracks to queue
            added_count = 0
            failed_count = 0

            for track in all_tracks:
                try:
                    # Create search query
                    artists = ", ".join([artist["name"] for artist in track["artists"]])
                    search_query = f"{track['name']} {artists}"

                    # Search for track
                    search_results = await wavelink.Playable.search(search_query)

                    if search_results:
                        found_track = search_results[0]
                        # Store requester for album track
                        self.store_track_requester(ctx.guild.id, found_track, ctx.author)
                        await vc.queue.put_wait(found_track)
                        added_count += 1
                    else:
                        failed_count += 1

                except Exception:
                    failed_count += 1

            # Start playing if not already playing
            if not vc.playing and not vc.queue.is_empty:
                track = await vc.queue.get_wait()
                await vc.play(track)
                await self.display_player_embed(vc, track, ctx)

            # Send summary
            summary = f"<:pass:1395855305270755399> Added **{added_count}** tracks from **{album_name}** to the queue."
            if failed_count > 0:
                summary += f"\n⚠️ Could not find **{failed_count}** tracks."

            await ctx.send(embed=discord.Embed(description=summary, color=0x333239))

        except Exception as e:
            await ctx.send(embed=discord.Embed(description="<:lcross:1393191711848529932>  Could not load this Spotify album.", color=0x333239))


async def setup(bot):
    await bot.add_cog(Music(bot))
